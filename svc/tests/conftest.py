import os
from datetime import datetime, timedelta, timezone
from typing import Any, AsyncGenerator, Dict
from uuid import UUID, uuid4

import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from svc.core.config.settings import get_settings

# 获取设置
settings = get_settings()
from fastapi.testclient import TestClient
from jose import jwt

from svc.core.database.base import Base
from svc.core.database.session import get_db
from svc.core.security.auth import create_access_token
from svc.main import app

engine = create_async_engine(
    settings.SQLALCHEMY_DATABASE_TEST_URI,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

TestingSessionLocal = sessionmaker(
    class_=AsyncSession,
    autocommit=False,
    autoflush=False,
    bind=engine,
)

@pytest_asyncio.fixture
async def db() -> AsyncGenerator[AsyncSession, None]:
    """创建测试数据库会话"""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    async with TestingSessionLocal() as session:
        yield session
        # 清理数据
        await session.rollback()
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

async def override_get_db() -> AsyncGenerator[AsyncSession, None]:
    """重写数据库依赖"""
    async with TestingSessionLocal() as session:
        yield session

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture
def client() -> TestClient:
    """创建测试客户端"""
    return TestClient(app)

@pytest.fixture
def test_user() -> Dict[str, Any]:
    """创建测试用户数据"""
    return {
        "id": uuid4(),
        "email": "<EMAIL>",
        "password": "testpassword",
        "is_active": True,
        "is_superuser": False,
        "tenant_id": "default",
        "full_name": "Test User",
    }

@pytest.fixture
def test_superuser() -> Dict[str, Any]:
    """创建测试管理员数据"""
    return {
        "id": uuid4(),
        "email": "<EMAIL>",
        "password": "adminpassword",
        "is_active": True,
        "is_superuser": True,
        "tenant_id": "default",
        "full_name": "Admin User",
    }

@pytest.fixture
def test_token(test_user: Dict[str, Any]) -> str:
    """创建测试用户的JWT token"""
    access_token = create_access_token(
        subject=str(test_user["id"]),
    )
    return access_token

@pytest.fixture
def test_superuser_token(test_superuser: Dict[str, Any]) -> str:
    """创建测试管理员的JWT token"""
    access_token = create_access_token(
        subject=str(test_superuser["id"]),
    )
    return access_token

@pytest.fixture
def authorized_client(client: TestClient, test_token: str) -> TestClient:
    """创建带有认证的测试客户端"""
    client.headers = {
        **client.headers,
        "Authorization": f"Bearer {test_token}"
    }
    return client

@pytest.fixture
def superuser_client(client: TestClient, test_superuser_token: str) -> TestClient:
    """创建带有管理员认证的测试客户端"""
    client.headers = {
        **client.headers,
        "Authorization": f"Bearer {test_superuser_token}"
    }
    return client

@pytest.fixture
def test_campaign():
    """营销活动测试数据"""
    return {
        "tenant_id": UUID("11111111-1111-1111-1111-111111111111"),
        "name": "Test Campaign",
        "type": "fission",
        "description": "Test campaign description",
        "start_time": datetime.now(timezone.utc),
        "end_time": datetime.now(timezone.utc) + timedelta(days=30),
        "invitation_limit": 10,
        "max_invitees": 100,
        "reward_rules": {
            "inviter_reward": 10.0,
            "invitee_reward": 5.0,
            "currency": "CNY"
        }
    }

@pytest.fixture
def test_invitation():
    """邀请测试数据"""
    return {
        "tenant_id": UUID("11111111-1111-1111-1111-111111111111"),
        "code": "TEST123",
        "expires_at": datetime.now(timezone.utc) + timedelta(days=7)
    }

@pytest.fixture
def test_reward():
    """奖励测试数据"""
    return {
        "tenant_id": UUID("11111111-1111-1111-1111-111111111111"),
        "type": "inviter_reward",
        "amount": 10.0,
        "currency": "CNY"
    }

@pytest.fixture
def test_role() -> Dict[str, Any]:
    """角色测试数据"""
    return {
        "name": "test_role",
        "description": "测试角色",
        "permissions": ["user:read", "user:create"]
    }

@pytest.fixture
def test_admin_role() -> Dict[str, Any]:
    """管理员角色测试数据"""
    return {
        "name": "admin_role",
        "description": "管理员角色",
        "permissions": ["user:read", "user:create", "user:update", "user:delete", 
                       "role:read", "role:create", "role:update", "role:delete"]
    }

@pytest.fixture
def test_updated_role() -> Dict[str, Any]:
    """角色更新测试数据"""
    return {
        "name": "updated_role",
        "description": "更新后的角色",
        "permissions": ["user:read", "user:create", "user:update"]
    } 