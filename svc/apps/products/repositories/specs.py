from typing import Any, List, Optional, Tuple, Union

from sqlalchemy import Column, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from svc.apps.products.models.specs import (ProductSpec,
                                            ProductSpecCombination,
                                            ProductSpecOption, Spec,
                                            SpecOption)
from svc.apps.products.schemas.specs import (ProductSpecCombinationCreate,
                                             ProductSpecCreate,
                                             ProductSpecOptionCreate,
                                             SpecCreate, SpecOptionCreate)
from svc.core.repositories import BaseRepository


class SpecRepository(BaseRepository[Spec, SpecCreate, SpecCreate]):
    """
    规格仓库类，提供规格的基本CRUD操作
    Only handles DB operations for Spec.
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, Spec)

    async def get_one(self, **filters) -> Optional[Spec]:
        """根据条件获取单个实体，并预加载options"""
        if not filters:
            return None

        query = select(self.model).options(selectinload(self.model.options))
        query = self._apply_filters(query, **filters)

        result = await self.db.execute(query)
        return result.scalars().first()

    async def get_list(
        self,
        *,
        skip: int = 0,
        limit: int = 100,
        order_by: Optional[Union[str, Column]] = None,
        order_direction: str = "asc",
        **filters
    ) -> List[Spec]:
        """获取实体列表，并预加载options"""
        query = self._build_query(order_by, order_direction, **filters).options(selectinload(self.model.options))

        query = query.offset(skip).limit(limit)

        result = await self.db.execute(query)
        return list(result.scalars().all())

    async def get_paginated(
        self,
        *,
        page_num: int = 1,
        page_size: int = 10,
        order_by: Optional[Union[str, Column]] = None,
        order_direction: str = "asc",
        with_total: bool = True,
        **filters
    ) -> Tuple[List[Spec], Optional[int]]:
        """获取分页的实体列表及总数，并预加载options"""
        skip = (page_num - 1) * page_size
        query = self._build_query(order_by, order_direction, **filters).options(selectinload(self.model.options))
        query = query.offset(skip).limit(page_size)
        result = await self.db.execute(query)
        items = list(result.scalars().all())
        total = None
        if with_total:
            total = await self.count(**filters)
        return items, total

    async def list_by_product(self, product_id: int, skip: int = 0, limit: int = 100) -> List[Spec]:
        """
        获取指定商品的所有规格，支持数据库级分页
        """
        return await self.get_list(product_id=product_id, skip=skip, limit=limit)

class SpecOptionRepository(BaseRepository[SpecOption, SpecOptionCreate, SpecOptionCreate]):
    """
    规格值仓库类，提供规格值的基本CRUD操作
    Only handles DB operations for SpecOption.
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, SpecOption)

    async def list_by_spec(self, spec_id: int, skip: int = 0, limit: int = 100) -> List[SpecOption]:
        """
        获取指定规格的所有规格值，支持数据库级分页
        """
        return await self.get_list(spec_id=spec_id, skip=skip, limit=limit)

class ProductSpecRepository(BaseRepository[ProductSpec, ProductSpecCreate, ProductSpecCreate]):
    """
    商品与规格关联仓库类
    Only handles DB operations for ProductSpec.
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, ProductSpec)

    async def list_by_product(self, product_id: int) -> List[ProductSpec]:
        """
        获取指定商品的所有规格关联
        """
        return await self.get_list(product_id=product_id)

class ProductSpecOptionRepository(BaseRepository[ProductSpecOption, ProductSpecOptionCreate, ProductSpecOptionCreate]):
    """
    商品与规格值关联仓库类
    Only handles DB operations for ProductSpecOption.
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, ProductSpecOption)

    async def list_by_product_spec(self, product_spec_id: int) -> List[ProductSpecOption]:
        """
        获取指定商品规格的所有规格值关联
        """
        return await self.get_list(product_spec_id=product_spec_id)

class ProductSpecCombinationRepository(BaseRepository[ProductSpecCombination, ProductSpecCombinationCreate, ProductSpecCombinationCreate]):
    """
    商品规格组合（可售单元）仓库类
    Handles all DB operations for ProductSpecCombination.
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, ProductSpecCombination)

    async def create_with_album(self, combination_data: ProductSpecCombinationCreate, album_id: int) -> ProductSpecCombination:
        """创建规格组合并关联图册ID"""
        new_combination = ProductSpecCombination(**combination_data.model_dump(), album_id=album_id)
        self.db.add(new_combination)
        await self.db.flush()
        await self.db.refresh(new_combination)
        return new_combination

    async def get_one(
        self, 
        **filters
    ) -> Optional[ProductSpecCombination]:
        """根据条件获取单个实体，并预加载图册"""
        if not filters:
            return None
        
        query = select(self.model).options(selectinload(self.model.album))
        query = self._apply_filters(query, **filters)
        
        result = await self.db.execute(query)
        return result.scalars().first()

    async def get_list(
        self, 
        *,
        skip: int = 0, 
        limit: int = 100,
        order_by: Optional[Union[str, Column]] = None,
        order_direction: str = "asc",
        **filters
    ) -> List[ProductSpecCombination]:
        """获取实体列表，并预加载图册"""
        query = self._build_query(order_by, order_direction, **filters).options(selectinload(self.model.album))
        
        query = query.offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        return list(result.scalars().all())

    async def list_by_product(self, product_id: int, skip: int = 0, limit: int = 100) -> List[ProductSpecCombination]:
        """
        获取指定商品的所有规格组合，支持数据库级分页
        """
        return await self.get_list(product_id=product_id, skip=skip, limit=limit)

    async def bulk_create(self, combinations: List[ProductSpecCombinationCreate]) -> List[ProductSpecCombination]:
        """
        批量插入商品规格组合
        """
        objs = [ProductSpecCombination(**combo.model_dump()) for combo in combinations]
        self.db.add_all(objs)
        await self.db.flush()
        return objs 